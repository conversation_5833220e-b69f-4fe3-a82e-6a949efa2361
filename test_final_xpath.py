#!/usr/bin/env python3
"""
最终测试：验证XPath支持并测试原始用例
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.tools.browser_tools import browser_evaluate
from iicrawlermcp.core.browser import get_global_browser, close_global_browser

def test_final_xpath():
    """最终XPath测试"""
    print("🧪 最终XPath支持测试")
    print("-" * 50)
    
    try:
        # 初始化浏览器
        browser = get_global_browser()
        
        # 导航到有链接的页面
        browser.navigate("https://httpbin.org/links/10/0")
        print("✅ 导航到链接测试页面成功")
        
        # 检查页面内容
        result = browser_evaluate.invoke({
            "function": "() => document.body.innerHTML.substring(0, 200) + '...'"
        })
        print(f"页面内容预览: {result}")
        
        # 测试1: 查找第一个链接
        print(f"\n📋 测试1: 查找第一个链接")
        result = browser_evaluate.invoke({
            "function": "(element) => element ? 'Found link: ' + element.textContent + ' (href: ' + element.href + ')' : 'Not found'", 
            "element": "第一个链接", 
            "ref": "//a[1]"
        })
        print(f"结果: {result}")
        
        # 测试2: 测试原始XPath格式（绝对路径）
        print(f"\n📋 测试2: 绝对XPath路径")
        result = browser_evaluate.invoke({
            "function": "(element) => element ? 'Found: ' + element.textContent : 'Not found'", 
            "element": "绝对路径链接", 
            "ref": "/html/body/a[1]"
        })
        print(f"结果: {result}")
        
        # 测试3: 模拟你的原始用例 - 点击操作
        print(f"\n📋 测试3: 点击操作模拟")
        result = browser_evaluate.invoke({
            "function": "(element) => { if(element) { console.log('Clicking element:', element); element.click(); return 'Click executed successfully'; } else { return 'Element not found for click'; } }", 
            "element": "第一个链接", 
            "ref": "//a[1]"
        })
        print(f"点击结果: {result}")
        
        # 测试4: 验证你的原始XPath格式是否支持
        original_xpath = "html/body/div[1]/div/main/div[2]/div[1]/div[1]/a[2]"
        print(f"\n📋 测试4: 原始XPath格式支持验证")
        print(f"XPath: {original_xpath}")
        
        # 现在应该可以正常工作了！
        result = browser_evaluate.invoke({
            "function": "(element) => element.click()", 
            "element": "小说榜区域的更多链接", 
            "ref": original_xpath
        })
        print(f"原始用例结果: {result}")
        
        print("\n✅ 所有测试完成 - XPath支持已启用！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理
        close_global_browser()

if __name__ == "__main__":
    test_final_xpath()
