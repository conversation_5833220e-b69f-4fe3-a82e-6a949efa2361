# 贡献指南

感谢你对 iICrawlerMCP 项目的关注！我们欢迎各种形式的贡献。

## 🤝 如何贡献

### 报告问题
- 使用 GitHub Issues 报告 bug
- 提供详细的重现步骤
- 包含环境信息（Python版本、操作系统等）

### 提出功能请求
- 在 Issues 中描述新功能的需求
- 解释为什么这个功能有用
- 提供使用场景示例

### 提交代码
1. Fork 项目仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📋 开发环境设置

### 1. 克隆项目
```bash
git clone https://github.com/your-username/iicrawlermcp.git
cd iicrawlermcp
```

### 2. 安装开发依赖
```bash
pip install -r requirements-dev.txt
playwright install
```

### 3. 设置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，添加你的配置
```

### 4. 运行测试
```bash
pytest tests/
```

## 🧪 测试指南

### 运行测试
```bash
# 运行所有测试
pytest tests/

# 运行特定类型的测试
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# 运行测试并生成覆盖率报告
pytest tests/ --cov=src/iicrawlermcp --cov-report=html
```

### 编写测试
- 为新功能编写单元测试
- 确保测试覆盖率不降低
- 测试文件命名：`test_*.py`
- 测试函数命名：`test_*`

## 📝 代码规范

### Python代码风格
- 遵循 PEP 8 规范
- 使用 Black 进行代码格式化
- 使用 flake8 进行代码检查
- 使用 mypy 进行类型检查

```bash
# 格式化代码
black src/ tests/

# 代码检查
flake8 src/ tests/

# 类型检查
mypy src/
```

### 提交信息规范
使用清晰的提交信息：
```
类型(范围): 简短描述

详细描述（可选）

- 新增功能使用 feat:
- 修复bug使用 fix:
- 文档更新使用 docs:
- 代码重构使用 refactor:
- 测试相关使用 test:
```

示例：
```
feat(agents): 添加BrowserAgent智能体

- 实现专门的浏览器控制智能体
- 支持直接方法调用和智能体调用
- 包含11个专门的浏览器工具
```

## 📚 文档贡献

### 文档结构
- 主文档位于 `docs/` 目录
- 使用 Markdown 格式
- 遵循清晰的目录结构

### 文档类型
- **API文档**: `docs/api/` - 详细的API参考
- **架构文档**: `docs/architecture/` - 系统设计文档
- **使用指南**: `docs/guides/` - 用户使用指南
- **开发文档**: `docs/development/` - 开发相关文档

### 文档规范
- 使用清晰的标题层级
- 提供代码示例
- 包含必要的截图或图表
- 保持内容简洁明了

## 🔍 代码审查

### Pull Request 要求
- 提供清晰的PR描述
- 包含相关的测试
- 确保所有检查通过
- 响应审查意见

### 审查标准
- 代码质量和可读性
- 测试覆盖率
- 文档完整性
- 性能影响
- 安全考虑

## 🏗️ 项目架构

### 目录结构
```
iICrawlerMCP/
├── src/iicrawlermcp/    # 源代码
├── tests/               # 测试代码
├── examples/            # 示例代码
├── docs/                # 项目文档
└── screenshots/         # 截图输出
```

### 核心模块
- **agents/**: 智能体实现
- **core/**: 核心功能模块
- **tools/**: LangChain工具
- **dom/**: DOM处理模块

## ❓ 获取帮助

- 查看 [文档](docs/README.md)
- 阅读 [快速开始指南](docs/getting-started.md)
- 在 Issues 中提问
- 查看现有的 Pull Requests

## 📄 许可证

通过贡献代码，你同意你的贡献将在与项目相同的许可证下发布。

---

再次感谢你的贡献！🎉
