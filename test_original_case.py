#!/usr/bin/env python3
"""
测试原始用例：点击小说榜区域的"更多"链接
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.tools.browser_tools import browser_evaluate
from iicrawlermcp.core.browser import get_global_browser, close_global_browser

def test_original_case():
    """测试原始用例"""
    print("🧪 测试原始用例：XPath点击操作")
    print("-" * 50)
    
    try:
        # 初始化浏览器
        browser = get_global_browser()
        
        # 导航到一个有链接的测试页面
        browser.navigate("https://httpbin.org/html")
        print("✅ 导航到测试页面成功")
        
        # 测试原始的XPath格式
        xpath = "html/body/div[1]/div/main/div[2]/div[1]/div[1]/a[2]"
        print(f"\n📋 测试XPath: {xpath}")
        
        # 先检查元素是否存在
        result = browser_evaluate.invoke({
            "function": "(element) => element ? 'Element found: ' + element.tagName : 'Element not found'", 
            "element": "测试元素", 
            "ref": f"//{xpath}"
        })
        print(f"元素检查结果: {result}")
        
        # 测试一个更简单的XPath
        simple_xpath = "//a[1]"
        print(f"\n📋 测试简单XPath: {simple_xpath}")
        
        result = browser_evaluate.invoke({
            "function": "(element) => element ? 'Found: ' + element.textContent + ' (href: ' + element.href + ')' : 'Not found'", 
            "element": "第一个链接", 
            "ref": simple_xpath
        })
        print(f"简单XPath结果: {result}")
        
        # 测试点击操作
        print(f"\n📋 测试点击操作")
        result = browser_evaluate.invoke({
            "function": "(element) => { if(element) { element.style.border = '3px solid red'; return 'Element highlighted - ready for click'; } else { return 'Element not found'; } }", 
            "element": "第一个链接", 
            "ref": simple_xpath
        })
        print(f"点击准备结果: {result}")
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理
        close_global_browser()

if __name__ == "__main__":
    test_original_case()
