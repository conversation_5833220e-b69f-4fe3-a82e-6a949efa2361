#!/usr/bin/env python3
"""
测试 browser_evaluate 工具的 XPath 支持
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from iicrawlermcp.tools.browser_tools import browser_evaluate
from iicrawlermcp.core.browser import get_global_browser, close_global_browser

def test_xpath_support():
    """测试XPath支持"""
    print("🧪 测试 browser_evaluate 的 XPath 支持")
    print("-" * 50)
    
    try:
        # 初始化浏览器
        browser = get_global_browser()
        
        # 导航到测试页面
        browser.navigate("https://httpbin.org/html")
        print("✅ 导航到测试页面成功")
        
        # 测试1: 无元素的JavaScript执行
        print("\n📋 测试1: 基础JavaScript执行")
        result = browser_evaluate.invoke({"function": "() => document.title"})
        print(f"结果: {result}")

        # 测试2: 使用XPath查找元素并获取文本
        print("\n📋 测试2: XPath元素查找和文本获取")
        result = browser_evaluate.invoke({
            "function": "(element) => element.textContent",
            "element": "页面标题",
            "ref": "//h1"
        })
        print(f"结果: {result}")

        # 测试3: 使用XPath查找链接并获取href
        print("\n📋 测试3: XPath链接查找")
        result = browser_evaluate.invoke({
            "function": "(element) => element.href",
            "element": "第一个链接",
            "ref": "//a[1]"
        })
        print(f"结果: {result}")

        # 测试4: 复杂XPath表达式
        print("\n📋 测试4: 复杂XPath表达式")
        result = browser_evaluate.invoke({
            "function": "(element) => element.tagName + ': ' + element.textContent.substring(0, 20) + '...'",
            "element": "包含特定文本的元素",
            "ref": "//p[contains(text(), 'Herman')]"
        })
        print(f"结果: {result}")

        # 测试5: 测试你的原始用例 - 点击操作
        print("\n📋 测试5: 点击操作测试")
        result = browser_evaluate.invoke({
            "function": "(element) => { element.style.border = '2px solid red'; return 'Element highlighted for click test'; }",
            "element": "第一个链接",
            "ref": "//a[1]"
        })
        print(f"结果: {result}")
        
        print("\n✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        # 清理
        close_global_browser()

if __name__ == "__main__":
    test_xpath_support()
