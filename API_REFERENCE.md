# iICrawlerMCP API参考

> 完整的API参考文档，包含所有Agent、工具和核心模块的详细说明

## 🤖 Agent API

### CrawlerAgent (主协调Agent)

#### 创建和使用
```python
from iicrawlermcp.agents import build_agent

# 创建Agent
agent = build_agent(
    tools=None,           # 可选：自定义工具列表
    verbose=None,         # 可选：详细模式
    llm_config=None       # 可选：LLM配置
)

# 执行任务
result = agent.invoke("任务描述")
print(result["output"])

# 清理资源
agent.cleanup()
```

#### 支持的任务类型
- 🌐 **复杂网页操作**: 多步骤浏览器任务
- 🤖 **智能任务分解**: 自动选择合适的Agent
- 📊 **数据提取**: 结构化数据采集
- 🔄 **工作流自动化**: 批量操作和处理

### BrowserAgent (浏览器专家)

#### 创建和使用
```python
from iicrawlermcp.agents import build_browser_agent

# 创建浏览器专家
browser_agent = build_browser_agent()

# 执行浏览器任务
result = browser_agent.invoke("导航到google.com并截图")
```

#### 专长领域
- 🌐 **页面导航**: URL跳转、前进后退
- 📸 **截图捕获**: 全页面、元素截图
- 🖱️ **基础交互**: 点击、输入、悬停
- 📄 **页面信息**: 标题、URL、状态获取

### ElementAgent (DOM分析专家)

#### 创建和使用
```python
from iicrawlermcp.agents import build_element_agent

# 创建DOM分析专家
element_agent = build_element_agent()

# 执行DOM分析任务
result = element_agent.invoke("分析页面上的所有表单元素")
```

#### 专长领域
- 🔍 **元素发现**: 智能元素定位和识别
- 📊 **结构分析**: DOM结构和层次分析
- 🎯 **精确交互**: 复杂元素操作
- 📋 **数据提取**: 结构化内容提取

## 🔧 工具API

### BrowserToolkit (浏览器工具包)

#### 工具获取
```python
from iicrawlermcp.tools.browser_tools import BrowserToolkit

# 获取基础工具（专门Agent使用）
basic_tools = BrowserToolkit.get_basic_tools()

# 获取高级工具（通用Agent使用）
advanced_tools = BrowserToolkit.get_advanced_tools()

# 获取所有工具
all_tools = BrowserToolkit.get_all_tools()
```

#### 基础工具集 (6个工具)

##### navigate_browser
```python
navigate_browser(url: str) -> str
```
- **功能**: 导航到指定URL
- **参数**: `url` - 目标URL
- **返回**: 成功消息
- **示例**: `navigate_browser("https://google.com")`

##### take_screenshot
```python
take_screenshot(filename: Optional[str] = None, full_page: bool = True) -> str
```
- **功能**: 截取页面截图
- **参数**: 
  - `filename` - 文件名（可选）
  - `full_page` - 是否全页面截图
- **返回**: 截图路径
- **示例**: `take_screenshot("google.png", full_page=True)`

##### get_page_info
```python
get_page_info() -> str
```
- **功能**: 获取当前页面信息
- **返回**: 页面标题和URL
- **示例**: `get_page_info()`

##### click_element
```python
click_element(element_selector: str, description: str = "") -> str
```
- **功能**: 点击页面元素
- **参数**:
  - `element_selector` - XPath选择器（必须从DOM工具获取）
  - `description` - 元素描述（可选）
- **返回**: 操作结果
- **示例**: `click_element("//button[@id='login-btn']", "登录按钮")`

##### type_text
```python
type_text(element_selector: str, text: str, description: str = "", clear_first: bool = True) -> str
```
- **功能**: 在元素中输入文本
- **参数**:
  - `element_selector` - XPath选择器（必须从DOM工具获取）
  - `text` - 输入文本
  - `description` - 元素描述（可选）
  - `clear_first` - 是否先清空
- **返回**: 操作结果
- **示例**: `type_text("//input[@id='username']", "john_doe", "用户名输入框")`

##### hover_element
```python
hover_element(element_selector: str, description: str = "") -> str
```
- **功能**: 悬停在元素上
- **参数**:
  - `element_selector` - XPath选择器（必须从DOM工具获取）
  - `description` - 元素描述（可选）
- **返回**: 操作结果
- **示例**: `hover_element("//div[@id='menu']", "导航菜单")`

#### 高级工具集 (10个工具)

##### browser_snapshot
```python
browser_snapshot() -> str
```
- **功能**: 捕获页面可访问性快照
- **返回**: 页面快照信息
- **特点**: 比截图包含更多结构信息

##### browser_wait_for
```python
browser_wait_for(time: Optional[float] = None, text: Optional[str] = None, text_gone: Optional[str] = None) -> str
```
- **功能**: 等待条件满足
- **参数**:
  - `time` - 等待时间（秒）
  - `text` - 等待文本出现
  - `text_gone` - 等待文本消失
- **返回**: 等待结果

##### browser_evaluate
```python
browser_evaluate(function: str, element: Optional[str] = None, ref: Optional[str] = None) -> str
```
- **功能**: 执行JavaScript代码
- **参数**:
  - `function` - JavaScript函数
  - `element` - 元素描述（可选）
  - `ref` - 元素选择器（可选）
- **返回**: 执行结果

### DOM工具集

#### 增强DOM工具
```python
from iicrawlermcp.tools.dom_tools_enhanced import get_enhanced_dom_tools

# 获取所有增强DOM工具
dom_tools = get_enhanced_dom_tools()
```

#### 核心DOM工具

##### dom_get_buttons_enhanced
```python
dom_get_buttons_enhanced(include_disabled: bool = False, include_hidden: bool = False, require_text: bool = True) -> str
```
- **功能**: 获取页面所有按钮
- **参数**:
  - `include_disabled` - 包含禁用按钮
  - `include_hidden` - 包含隐藏按钮
  - `require_text` - 要求有文本内容
- **返回**: 格式化的按钮列表

##### dom_get_inputs_enhanced
```python
dom_get_inputs_enhanced(include_disabled: bool = False, include_hidden: bool = False) -> str
```
- **功能**: 获取页面所有输入框
- **返回**: 格式化的输入框列表

##### dom_get_links_enhanced
```python
dom_get_links_enhanced(include_empty: bool = False, max_results: int = 50) -> str
```
- **功能**: 获取页面所有链接
- **参数**:
  - `include_empty` - 包含空链接
  - `max_results` - 最大结果数
- **返回**: 格式化的链接列表

### 委托工具集

#### Agent委托 (已废弃)
```python
# 注意：delegate_to_browser_agent 和 delegate_to_element_agent 已被移除
# 请使用下面的智能工具选择方式
```

#### 智能工具选择
```python
from iicrawlermcp.tools.delegation_tools import smart_element_finder, smart_browser_action_finder

# 智能元素查找
result = smart_element_finder("搜索框")

# 智能浏览器操作
result = smart_browser_action_finder("点击登录按钮")
```

## 🌐 核心模块API

### Browser模块

#### 全局浏览器实例
```python
from iicrawlermcp.core.browser import get_global_browser, close_global_browser

# 获取全局浏览器实例
browser = get_global_browser()

# 使用浏览器
browser.navigate("https://example.com")
screenshot_path = browser.screenshot()

# 关闭浏览器
close_global_browser()
```

#### Browser类方法
```python
class Browser:
    def navigate(self, url: str) -> str:
        """导航到URL"""
        
    def screenshot(self, path: str = None, full_page: bool = True) -> str:
        """截取截图"""
        
    def click(self, selector: str) -> str:
        """点击元素"""
        
    def type_text(self, selector: str, text: str, clear_first: bool = True) -> str:
        """输入文本"""
        
    def get_page_info(self) -> dict:
        """获取页面信息"""
        
    def close(self) -> None:
        """关闭浏览器"""
```

### 配置模块

#### 配置管理
```python
from iicrawlermcp.core.config import config

# 访问配置
api_key = config.openai_api_key
model = config.openai_model
headless = config.headless

# 验证配置
config.validate()
```

#### 环境变量
```env
# 必需配置
OPENAI_API_KEY=your_api_key

# 可选配置
OPENAI_MODEL=gpt-4-turbo
OPENAI_API_BASE=https://api.openai.com/v1
HEADLESS=true
VERBOSE=true
DEFAULT_SCREENSHOT_PATH=screenshots/
BROWSER_TIMEOUT=60000
```

## 📊 数据结构

### ElementInfo
```python
@dataclass
class ElementInfo:
    tag_name: str           # 标签名
    text: str              # 文本内容
    attributes: dict       # 属性字典
    is_visible: bool       # 是否可见
    is_interactive: bool   # 是否可交互
    selector: str          # CSS选择器
    xpath: str            # XPath选择器
```

### HandoffResponse
```python
@dataclass
class HandoffResponse:
    final_result: str      # 最终结果
    handoff_count: int     # 移交次数
    execution_path: List[str]  # 执行路径
    success: bool          # 是否成功
```

## 🔧 使用最佳实践

### 1. Agent选择策略
- **简单浏览器操作** → 使用 `BrowserAgent`
- **复杂DOM分析** → 使用 `ElementAgent`
- **多步骤任务** → 使用 `CrawlerAgent`

### 2. 错误处理
```python
try:
    agent = build_agent()
    result = agent.invoke("任务描述")
    print(result["output"])
except Exception as e:
    print(f"任务执行失败: {e}")
finally:
    agent.cleanup()
```

### 3. 资源管理
```python
# 推荐：使用上下文管理器
with build_agent() as agent:
    result = agent.invoke("任务描述")
    
# 或者手动清理
agent = build_agent()
try:
    result = agent.invoke("任务描述")
finally:
    agent.cleanup()
```

## 📸 截图管理

### 截图路径配置
```python
from iicrawlermcp.core.config import config

# 获取项目根目录
project_root = config.get_project_root()

# 获取截图目录
screenshots_dir = config.get_screenshots_dir()

# 获取子目录截图路径
test_screenshots = config.get_screenshots_dir("tests")
```

### 截图最佳实践
```python
# 1. 使用描述性文件名
screenshot_path = browser.screenshot("login_page_2024.png")

# 2. 组织截图到子目录
test_screenshot = browser.screenshot("tests/form_validation.png")

# 3. 自动生成时间戳文件名
auto_screenshot = browser.screenshot()  # 自动生成文件名
```

### 截图目录结构
```
screenshots/
├── tests/              # 测试截图
├── examples/           # 示例截图
├── debug/              # 调试截图
└── production/         # 生产截图
```

## 🔍 常见问题解答

### Q: 如何自定义工具集？
```python
from iicrawlermcp.tools.browser_tools import BrowserToolkit
from iicrawlermcp.agents import build_agent

# 创建自定义工具集
custom_tools = BrowserToolkit.get_basic_tools()
custom_tools.extend([your_custom_tool])

# 使用自定义工具创建Agent
agent = build_agent(tools=custom_tools)
```

### Q: 如何处理动态内容？
```python
# 使用等待工具
browser_wait_for(text="加载完成")

# 或使用JavaScript执行
browser_evaluate("return document.readyState === 'complete'")
```

### Q: 如何优化性能？
```python
# 1. 复用浏览器实例
browser = get_global_browser()

# 2. 使用基础工具（更快）
basic_tools = BrowserToolkit.get_basic_tools()

# 3. 批量操作
agent.invoke("批量处理多个页面")
```

### Q: 如何调试Agent行为？
```python
# 启用详细模式
agent = build_agent(verbose=True)

# 查看执行日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

---

*完整的API参考确保您能够充分利用iICrawlerMCP的所有功能。如需更多示例，请查看 [examples/](examples/) 目录。*
