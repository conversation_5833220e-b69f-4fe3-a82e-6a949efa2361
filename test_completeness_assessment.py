#!/usr/bin/env python3
"""
测试修改后的 dom_assess_information_completeness 函数
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_completeness_assessment():
    """测试完整性评估函数"""
    try:
        from src.iicrawlermcp.tools.dom_tools_enhanced import dom_assess_information_completeness
        
        print("🧪 测试LLM驱动的完整性评估...")
        
        # 测试案例1：你的小说榜案例
        print("\n📚 测试案例1：小说榜完整性评估")
        user_query = "找到小说榜完整小说榜单的排名,书名,作者,类型,介绍"
        current_count = 10
        
        result = dom_assess_information_completeness(
            user_query=user_query,
            current_results_count=current_count,
            check_navigation=True
        )
        
        print(f"用户查询：{user_query}")
        print(f"当前结果数量：{current_count}")
        print(f"评估结果：{result}")
        
        # 测试案例2：简单的按钮查找
        print("\n🔘 测试案例2：按钮查找完整性评估")
        user_query2 = "找到登录按钮"
        current_count2 = 3
        
        result2 = dom_assess_information_completeness(
            user_query=user_query2,
            current_results_count=current_count2,
            check_navigation=False
        )
        
        print(f"用户查询：{user_query2}")
        print(f"当前结果数量：{current_count2}")
        print(f"评估结果：{result2}")
        
        print("\n✅ 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_completeness_assessment()
