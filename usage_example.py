#!/usr/bin/env python3
"""
使用示例：展示如何使用改造后的 browser_evaluate 工具
"""

from iicrawlermcp.tools.browser_tools import browser_evaluate

# 你的原始用例现在应该可以正常工作：
result = browser_evaluate.invoke({
    'function': '(element) => element.click()',
    'element': '小说榜区域的"更多"链接',
    'ref': 'html/body/div[1]/div/main/div[2]/div[1]/div[1]/a[2]'
})

print(f"点击结果: {result}")

# 其他XPath使用示例：

# 1. 获取元素文本
result = browser_evaluate.invoke({
    'function': '(element) => element.textContent',
    'element': '标题元素',
    'ref': '//h1'
})

# 2. 获取链接地址
result = browser_evaluate.invoke({
    'function': '(element) => element.href',
    'element': '第一个链接',
    'ref': '//a[1]'
})

# 3. 设置元素样式
result = browser_evaluate.invoke({
    'function': '(element) => { element.style.backgroundColor = "yellow"; return "已高亮"; }',
    'element': '目标按钮',
    'ref': '//button[contains(text(), "提交")]'
})

# 4. 获取表单值
result = browser_evaluate.invoke({
    'function': '(element) => element.value',
    'element': '输入框',
    'ref': '//input[@name="username"]'
})

# 5. 复杂XPath表达式
result = browser_evaluate.invoke({
    'function': '(element) => element.click()',
    'element': '包含特定文本的链接',
    'ref': '//a[contains(text(), "更多") and contains(@class, "more-link")]'
})
